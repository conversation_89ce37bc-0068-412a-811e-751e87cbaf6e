global with sharing class urcs_DC_GetSinistri implements System.Callable {

    public Object call(String action, Map<String,Object> args){
        Map<String,Object> input = (Map<String,Object>) args.get('input');
        Map<String,Object> output = (Map<String,Object>) args.get('output');
        Map<String,Object> options = (Map<String,Object>) args.get('options');
        invokeMethod(input, output, options);
        return null;
    }
        
    @AuraEnabled
    global static Map<String,Object> invokeMethod(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        
        system.debug('input '+ JSON.serialize(input));
        system.debug('output '+ JSON.serialize(output));
        system.debug('options '+ JSON.serialize(options));
        Map<String,Object> setValueMap = (Map<String,Object>) input.get('Set Values');
        String method = (String) setValueMap.get('methodExecute');
        try{
            if(method.equalsIgnoreCase('init')){
                output = getSinistri(input,output,options);
            }
        }catch(Exception e){
            output.put('error',e.getMessage() + ' ' + e.getStackTraceString());
        }
        return output;
    }
    
    public static Map<String,Object> getSinistri(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        try{
            String idVeicolo =  null != (String)input.get('recordId') ? (String)input.get('recordId') : null;

            if (!isDataCloudAvailable()) {
                return handleDataCloudUnavailable(output);
            }

            if (String.isBlank(idVeicolo)) {
                output.put('status', 'ERROR');
                output.put('message', 'Record ID is required');
                output.put('records', new List<Object>());
                return output;
            }


            List<Asset> assetList = [SELECT id, idAutoLeo__c FROM Asset WHERE id = :idVeicolo LIMIT 1];

            if (assetList.isEmpty()) {
                output.put('status', 'ERROR');
                output.put('message', 'Asset record not found');
                output.put('records', new List<Object>());
                return output;
            }

            Asset asset = assetList[0];
            if (String.isBlank(asset.idAutoLeo__c)) {
                output.put('status', 'ERROR');
                output.put('message', 'Asset does not have a valid idAutoLeo value');
                output.put('records', new List<Object>());
                return output;
            }

            Integer idAuto;
            try {
                idAuto = Integer.valueOf(asset.idAutoLeo__c);
            } catch (TypeException te) {
                output.put('status', 'ERROR');
                output.put('message', 'Invalid idAutoLeo format: ' + asset.idAutoLeo__c);
                output.put('records', new List<Object>());
                return output;
            }

            System.debug('idVeicolo: ' + idVeicolo + ', idAuto: ' + idAuto);


            String queryString = 'SELECT ID_SINISTRO__c,'+
                                    'ID_SINISTRO_WB__c, '+
                                    'DS_STATO_SINISTRO__c,' +
                                    'ID_CONTRATTO__c,'+
                                    'ID_AUTO__c, '+
                                    'DS_DRIVER_DENOMINAZIONECOMPUTED__c, '+
                                    'INTERVENTO_DATA_INVIO__c, '+
                                    'DS_ULTIMA_SORGENTE__c, '+
                                    'TS_INVIO_WB__c, '+
                                    'TS_DENUNCIA__c, '+
                                    'TS_CREAZIONE__c, '+
                                    'TS_CHIUSURA__c, '+
                                    'DS_CANALE_APERTURA__c ' +
                                    'FROM UR_SINISTRI__dlm ' +
                                    'WHERE ID_AUTO__c = :idAuto ';

            List<sObject> listSinistri = Database.query(queryString);


            output.put('status', 'SUCCESS');
            output.put('message', 'Data retrieved successfully');
            output.put('records', listSinistri);

            System.debug('listSinistri: ' + listSinistri);
            System.debug('output: ' + JSON.serializePretty(output));

        } catch (QueryException qe) {

            if (qe.getMessage().contains('UR_SINISTRI__dlm') ||
                qe.getMessage().toLowerCase().contains('data cloud') ||
                qe.getMessage().toLowerCase().contains('dlm')) {
                return handleDataCloudUnavailable(output);
            } else {
                output.put('status', 'ERROR');
                output.put('message', 'Query error: ' + qe.getMessage());
                output.put('records', new List<Object>());
            }
        } catch (Exception e) {
            output.put('status', 'ERROR');
            output.put('message', 'Unexpected error: ' + e.getMessage());
            output.put('records', new List<Object>());
            System.debug('Exception in getSinistri: ' + e.getMessage() + '\n' + e.getStackTraceString());
        }

        return output;
    }

    private static Boolean isDataCloudAvailable() {
        try {

            Map<String, Schema.SObjectType> globalDescribe = Schema.getGlobalDescribe();
            if (!globalDescribe.containsKey('UR_SINISTRI__dlm')) {
                System.debug('UR_SINISTRI__dlm object not found in schema');
                return false;
            }

            Schema.SObjectType sObjectType = globalDescribe.get('UR_SINISTRI__dlm');
            Schema.DescribeSObjectResult describeResult = sObjectType.getDescribe();

            if (!describeResult.isAccessible()) {
                System.debug('UR_SINISTRI__dlm object is not accessible');
                return false;
            }

            return true;

        } catch (Exception e) {
            System.debug('Error checking Data Cloud availability: ' + e.getMessage());
            return false;
        }
    }


    private static Map<String,Object> handleDataCloudUnavailable(Map<String,Object> output) {
        output.put('status', 'DATA_CLOUD_UNAVAILABLE');
        output.put('message', 'Data Cloud is not available in this org. Claims data is temporarily unavailable.');
        output.put('records', new List<Object>());

        System.debug('Data Cloud unavailable - returning structured response');
        System.debug('output: ' + JSON.serializePretty(output));
        return output;
    }
}