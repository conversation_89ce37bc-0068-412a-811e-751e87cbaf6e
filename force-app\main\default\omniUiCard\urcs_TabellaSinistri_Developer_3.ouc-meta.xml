<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Developer</authorName>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;Remote\&quot;][\&quot;records\&quot;]&quot;,&quot;ipMethod&quot;:&quot;URCS_Sinistri&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;02i9V000006yF7BQAU&quot;,&quot;id&quot;:1}]}}</dataSourceConfig>
    <isActive>false</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>urcs_TabellaSinistri</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:&quot;false&quot;,&quot;issortavailable&quot;:&quot;true&quot;,&quot;records&quot;:&quot;{records}&quot;,&quot;columns&quot;:[]},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Data Table-0&quot;},{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:false,&quot;issortavailable&quot;:true,&quot;cellLevelEdit&quot;:true,&quot;pagelimit&quot;:3,&quot;groupOrder&quot;:&quot;asc&quot;,&quot;searchDatatable&quot;:&quot;&quot;,&quot;columns&quot;:[{&quot;fieldName&quot;:&quot;ID_CONTRATTO__c&quot;,&quot;label&quot;:&quot;ID_CONTRATTO__c&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true},{&quot;fieldName&quot;:&quot;ID_SINISTRO__c&quot;,&quot;label&quot;:&quot;ID_SINISTRO__c&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true},{&quot;fieldName&quot;:&quot;ID_AUTO__c&quot;,&quot;label&quot;:&quot;ID_AUTO__c&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true},{&quot;fieldName&quot;:&quot;Id&quot;,&quot;label&quot;:&quot;Id&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true}],&quot;records&quot;:&quot;{records}&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Data Table-1&quot;},{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:&quot;false&quot;,&quot;issortavailable&quot;:&quot;true&quot;,&quot;records&quot;:&quot;{records}&quot;,&quot;columns&quot;:[]},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Data Table-3&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;Remote\&quot;][\&quot;records\&quot;]&quot;,&quot;ipMethod&quot;:&quot;URCS_Sinistri&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;02i9V000006yF7BQAU&quot;,&quot;id&quot;:1}]},&quot;title&quot;:&quot;urcs_TabellaSinistri&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;urcs_TabellaSinistri&quot;,&quot;apiVersion&quot;:61,&quot;targetConfigs&quot;:&quot;CiAgICA8dGFyZ2V0Q29uZmlnIHhtbG5zPSJodHRwOi8vc29hcC5zZm9yY2UuY29tLzIwMDYvMDQvbWV0YWRhdGEiIHRhcmdldHM9ImxpZ2h0bmluZ19fQXBwUGFnZSI+CiAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICA8cHJvcGVydHkgbmFtZT0icmVjb3JkSWQiIHR5cGU9IlN0cmluZyIvPgogICAgPC90YXJnZXRDb25maWc+CiAgICA8dGFyZ2V0Q29uZmlnIHhtbG5zPSJodHRwOi8vc29hcC5zZm9yY2UuY29tLzIwMDYvMDQvbWV0YWRhdGEiIHRhcmdldHM9ImxpZ2h0bmluZ19fUmVjb3JkUGFnZSI+CiAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICA8b2JqZWN0cyB4bWxucz0iIj4KICAgICAgICA8b2JqZWN0PkFzc2V0PC9vYmplY3Q+CiAgICAgIDwvb2JqZWN0cz4KICAgIDwvdGFyZ2V0Q29uZmlnPgogIA==&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;isExplicitImport&quot;:false,&quot;description&quot;:&quot;&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}}],&quot;objects&quot;:{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;&quot;},&quot;object&quot;:&quot;Asset&quot;}}]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;RemoteStatus&quot;:true,&quot;Set ValuesStatus&quot;:true,&quot;Remote&quot;:{&quot;error&quot;:&quot;OK&quot;,&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;records&quot;:[{&quot;ID_CONTRATTO__c&quot;:1095593,&quot;ID_SINISTRO__c&quot;:238690,&quot;ID_AUTO__c&quot;:405287,&quot;Id&quot;:&quot;qDS000004066584GAA&quot;,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_SINISTRI__dlm/qDS000004066584GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}}]},&quot;Set Values&quot;:{&quot;methodExecute&quot;:&quot;init&quot;,&quot;recordId&quot;:&quot;02i9V000006yF7BQAU&quot;},&quot;options&quot;:{&quot;forceQueueable&quot;:false,&quot;mockHttpResponse&quot;:null,&quot;vlcApexResponse&quot;:true,&quot;useFuture&quot;:false,&quot;isTestProcedure&quot;:false,&quot;resetCache&quot;:false,&quot;integrationProcedureKey&quot;:null,&quot;vlcIPData&quot;:null,&quot;OmniAnalyticsTrackingDebug&quot;:false,&quot;ignoreCache&quot;:false,&quot;shouldCommit&quot;:false,&quot;vlcTestSuiteUniqueKey&quot;:null,&quot;vlcTestUniqueKey&quot;:null,&quot;vlcCacheKey&quot;:null,&quot;continuationStepResult&quot;:null,&quot;vlcFilesMap&quot;:null,&quot;ParentInteractionToken&quot;:null,&quot;useQueueable&quot;:false,&quot;disableMetadataCache&quot;:false,&quot;isDebug&quot;:false,&quot;queueableChainable&quot;:false,&quot;useContinuation&quot;:false,&quot;chainable&quot;:false,&quot;ignoreMetadataPermissions&quot;:false,&quot;useHttpCalloutMock&quot;:false,&quot;useQueueableApexRemoting&quot;:false},&quot;response&quot;:{},&quot;ResponseStatus&quot;:true,&quot;recordId&quot;:&quot;02i9V000006yF7BQAU&quot;}</sampleDataSourceResponse>
    <stylingConfiguration>{}</stylingConfiguration>
    <versionNumber>3</versionNumber>
</OmniUiCard>
