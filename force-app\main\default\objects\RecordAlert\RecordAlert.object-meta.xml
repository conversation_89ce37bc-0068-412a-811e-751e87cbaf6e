<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>Private</externalSharingModel>
    <searchLayouts>
        <customTabListAdditionalFields>Parent</customTabListAdditionalFields>
        <customTabListAdditionalFields>What</customTabListAdditionalFields>
        <customTabListAdditionalFields>SourceSystemIdentifier</customTabListAdditionalFields>
        <customTabListAdditionalFields>Subject</customTabListAdditionalFields>
        <customTabListAdditionalFields>Description</customTabListAdditionalFields>
        <customTabListAdditionalFields>EffectiveDate</customTabListAdditionalFields>
        <customTabListAdditionalFields>ValidUntilDate</customTabListAdditionalFields>
        <customTabListAdditionalFields>Severity</customTabListAdditionalFields>
        <customTabListAdditionalFields>IsActive</customTabListAdditionalFields>
        <customTabListAdditionalFields>RecordAlertCategory</customTabListAdditionalFields>
        <lookupDialogsAdditionalFields>Parent</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>What</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>SourceSystemIdentifier</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Subject</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Description</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>EffectiveDate</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>ValidUntilDate</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Severity</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>IsActive</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>RecordAlertCategory</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Parent</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>What</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>SourceSystemIdentifier</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Subject</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Description</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>EffectiveDate</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ValidUntilDate</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Severity</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>IsActive</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>RecordAlertCategory</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>Parent</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>What</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>SourceSystemIdentifier</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Subject</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Description</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>EffectiveDate</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ValidUntilDate</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Severity</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>IsActive</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>RecordAlertCategory</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
