<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>ReadWrite</externalSharingModel>
    <searchLayouts>
        <customTabListAdditionalFields>DataGraphApiName</customTabListAdditionalFields>
        <customTabListAdditionalFields>PrimaryDmo</customTabListAdditionalFields>
        <customTabListAdditionalFields>DataSpace</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastRunStatus</customTabListAdditionalFields>
        <customTabListAdditionalFields>DataGraphType</customTabListAdditionalFields>
        <customTabListAdditionalFields>Schedule</customTabListAdditionalFields>
        <customTabListAdditionalFields>RecordCount</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastRefreshDate</customTabListAdditionalFields>
        <customTabListAdditionalFields>CreatedDate</customTabListAdditionalFields>
        <lookupDialogsAdditionalFields>DataGraphApiName</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>PrimaryDmo</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>DataSpace</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>LastRunStatus</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>DataGraphType</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Schedule</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>RecordCount</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>LastRefreshDate</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>CreatedDate</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>DataGraphApiName</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>PrimaryDmo</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>DataSpace</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LastRunStatus</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>DataGraphType</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Schedule</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>RecordCount</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LastRefreshDate</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CreatedDate</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>DataGraphApiName</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>PrimaryDmo</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>DataSpace</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastRunStatus</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>DataGraphType</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Schedule</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>RecordCount</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastRefreshDate</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CreatedDate</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>ReadWrite</sharingModel>
</CustomObject>
