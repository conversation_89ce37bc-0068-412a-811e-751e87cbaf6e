<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>DeleteSurvey</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>DeleteSurvey</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>DeleteSurvey</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>NewSurvey</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>NewSurvey</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>NewSurvey</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>OpenLatestVersion</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>OpenLatestVersion</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>OpenLatestVersion</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>Private</externalSharingModel>
    <searchLayouts>
        <customTabListAdditionalFields>LastModifiedDate</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastModifiedBy</customTabListAdditionalFields>
        <customTabListAdditionalFields>TotalVersionsCount</customTabListAdditionalFields>
        <lookupDialogsAdditionalFields>LastModifiedDate</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>LastModifiedBy</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>TotalVersionsCount</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LastModifiedDate</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LastModifiedBy</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>TotalVersionsCount</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedDate</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedBy</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>TotalVersionsCount</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
