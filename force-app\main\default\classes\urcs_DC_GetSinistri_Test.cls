@isTest
public class urcs_DC_GetSinistri_Test {
    

    @TestSetup
    static void setupTestData() {
        Asset testAsset = new Asset(
            Name = 'Test Vehicle Asset',
            idAutoLeo__c = '12345'
        );
        insert testAsset;
        
        // Create test UR_SINISTRI__dlm records
        List<sObject> testSinistri = new List<sObject>();
        
        for (Integer i = 1; i <= 3; i++) {
            sObject sinistro = Schema.getGlobalDescribe().get('UR_SINISTRI__dlm').newSObject();
            sinistro.put('ID_AUTO__c', 12345);
            sinistro.put('ID_SINISTRO__c', 'SIN00' + i);
            sinistro.put('ID_SINISTRO_WB__c', 'WB00' + i);
            sinistro.put('DS_STATO_SINISTRO__c', 'APERTO');
            sinistro.put('ID_CONTRATTO__c', 'CONTR00' + i);
            sinistro.put('DS_DRIVER_DENOMINAZIONECOMPUTED__c', 'Test Driver ' + i);
            sinistro.put('INTERVENTO_DATA_INVIO__c', Date.today().addDays(-i));
            sinistro.put('DS_ULTIMA_SORGENTE__c', 'TEST_SOURCE');
            sinistro.put('TS_INVIO_WB__c', DateTime.now().addDays(-i));
            sinistro.put('TS_DENUNCIA__c', DateTime.now().addDays(-i));
            sinistro.put('TS_CREAZIONE__c', DateTime.now().addDays(-i));
            sinistro.put('TS_CHIUSURA__c', null);
            sinistro.put('DS_CANALE_APERTURA__c', 'WEB');
            testSinistri.add(sinistro);
        }
        
        insert testSinistri;
    }
    
    @isTest
    static void testCallMethodSuccess() {
        // Get test data
        Asset testAsset = [SELECT Id FROM Asset LIMIT 1];
        
        // Prepare test parameters
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };
        
        urcs_DC_GetSinistri controller = new urcs_DC_GetSinistri();
        
        Test.startTest();
        Object result = controller.call('testAction', args);
        Test.stopTest();

    }
    
    @isTest
    static void testInvokeMethodWithInitSuccess() {
        // Get test data
        Asset testAsset = [SELECT Id FROM Asset LIMIT 1];
        
        // Prepare test parameters
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.invokeMethod(input, output, options);
        Test.stopTest();
    }
    
    @isTest
    static void testGetSinistriSuccess() {
        Asset testAsset = [SELECT Id FROM Asset LIMIT 1];
        
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.getSinistri(input, output, options);
        Test.stopTest();
        
    }
    
    @isTest
    static void testGetSinistriWithInvalidAssetId() {
        // Use a fake Asset ID that doesn't exist
        String fakeAssetId = '001000000000000AAA';
        
        // Prepare test parameters
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => fakeAssetId
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.getSinistri(input, output, options);
        Test.stopTest();
        
    }
    
    @isTest
    static void testGetSinistriWithNullRecordId() {
        // Prepare test parameters with null recordId
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => null
        };
        
        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();
        
        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.getSinistri(input, output, options);
        Test.stopTest();
    }
    
}
