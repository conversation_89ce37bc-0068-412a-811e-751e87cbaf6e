/**
 * @description Comprehensive test class for urcs_DC_GetSinistri with Data Cloud availability checks
 * <AUTHOR> Test Class
 * @date 2025-07-11
 */
@isTest
public class urcs_DC_GetSinistri_Test {

    @TestSetup
    static void setupTestData() {
        // Create test Asset records
        List<Asset> testAssets = new List<Asset>();

        // Asset with valid idAutoLeo__c
        testAssets.add(new Asset(
            Name = 'Test Vehicle Asset',
            idAutoLeo__c = '12345'
        ));

        // Asset with null idAutoLeo__c
        testAssets.add(new Asset(
            Name = 'Test Asset Null ID',
            idAutoLeo__c = null
        ));

        // Asset with invalid idAutoLeo__c
        testAssets.add(new Asset(
            Name = 'Test Asset Invalid ID',
            idAutoLeo__c = 'INVALID_NUMBER'
        ));

        insert testAssets;

        // Only create UR_SINISTRI__dlm records if Data Cloud object exists
        // This prevents test failures in orgs without Data Cloud
        try {
            Map<String, Schema.SObjectType> globalDescribe = Schema.getGlobalDescribe();
            if (globalDescribe.containsKey('UR_SINISTRI__dlm')) {
                List<sObject> testSinistri = new List<sObject>();

                for (Integer i = 1; i <= 3; i++) {
                    sObject sinistro = globalDescribe.get('UR_SINISTRI__dlm').newSObject();
                    sinistro.put('ID_AUTO__c', 12345);
                    sinistro.put('ID_SINISTRO__c', 'SIN00' + i);
                    sinistro.put('ID_SINISTRO_WB__c', 'WB00' + i);
                    sinistro.put('DS_STATO_SINISTRO__c', 'APERTO');
                    sinistro.put('ID_CONTRATTO__c', 'CONTR00' + i);
                    sinistro.put('DS_DRIVER_DENOMINAZIONECOMPUTED__c', 'Test Driver ' + i);
                    sinistro.put('INTERVENTO_DATA_INVIO__c', Date.today().addDays(-i));
                    sinistro.put('DS_ULTIMA_SORGENTE__c', 'TEST_SOURCE');
                    sinistro.put('TS_INVIO_WB__c', DateTime.now().addDays(-i));
                    sinistro.put('TS_DENUNCIA__c', DateTime.now().addDays(-i));
                    sinistro.put('TS_CREAZIONE__c', DateTime.now().addDays(-i));
                    sinistro.put('TS_CHIUSURA__c', null);
                    sinistro.put('DS_CANALE_APERTURA__c', 'WEB');
                    testSinistri.add(sinistro);
                }

                insert testSinistri;
            }
        } catch (Exception e) {
            // If Data Cloud objects don't exist, that's fine - we'll test the unavailable scenario
            System.debug('Data Cloud objects not available in test setup: ' + e.getMessage());
        }
    }
    
    /**
     * @description Test the call method with valid parameters
     */
    @isTest
    static void testCallMethodSuccess() {
        // Get test data
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];

        // Prepare test parameters
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Map<String, Object> args = new Map<String, Object>{
            'input' => input,
            'output' => output,
            'options' => options
        };

        urcs_DC_GetSinistri controller = new urcs_DC_GetSinistri();

        Test.startTest();
        Object result = controller.call('testAction', args);
        Test.stopTest();

        // Assertions
        System.assertEquals(null, result, 'Call method should return null');
        System.assert(output.containsKey('status'), 'Output should contain status');
        System.assert(output.containsKey('records'), 'Output should contain records');
    }

    /**
     * @description Test the invokeMethod with valid init method
     */
    @isTest
    static void testInvokeMethodWithInitSuccess() {
        // Get test data
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];

        // Prepare test parameters
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id,
            'Set Values' => new Map<String, Object>{
                'methodExecute' => 'init'
            }
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.invokeMethod(input, output, options);
        Test.stopTest();

        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assert(result.containsKey('status'), 'Result should contain status');
        System.assert(result.containsKey('records'), 'Result should contain records');
    }

    /**
     * @description Test getSinistri with valid Asset ID when Data Cloud is available
     */
    @isTest
    static void testGetSinistriSuccess() {
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.getSinistri(input, output, options);
        Test.stopTest();

        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assert(result.containsKey('status'), 'Result should contain status');
        System.assert(result.containsKey('records'), 'Result should contain records');

        String status = (String) result.get('status');
        System.assert(status == 'SUCCESS' || status == 'DATA_CLOUD_UNAVAILABLE',
                     'Status should be SUCCESS or DATA_CLOUD_UNAVAILABLE');
    }

    /**
     * @description Test getSinistri with invalid Asset ID
     */
    @isTest
    static void testGetSinistriWithInvalidAssetId() {
        // Use a fake Asset ID that doesn't exist
        String fakeAssetId = '001000000000000AAA';

        // Prepare test parameters
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => fakeAssetId
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.getSinistri(input, output, options);
        Test.stopTest();

        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assert(result.containsKey('status'), 'Result should contain status');

        String status = (String) result.get('status');
        System.assert(status == 'ERROR' || status == 'DATA_CLOUD_UNAVAILABLE',
                     'Status should be ERROR or DATA_CLOUD_UNAVAILABLE for invalid Asset ID');
    }

    /**
     * @description Test getSinistri with null recordId
     */
    @isTest
    static void testGetSinistriWithNullRecordId() {
        // Prepare test parameters with null recordId
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => null
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.getSinistri(input, output, options);
        Test.stopTest();

        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assert(result.containsKey('status'), 'Result should contain status');

        String status = (String) result.get('status');
        System.assert(status == 'ERROR' || status == 'DATA_CLOUD_UNAVAILABLE',
                     'Status should be ERROR or DATA_CLOUD_UNAVAILABLE for null recordId');
    }

    /**
     * @description Test getSinistri with Asset that has null idAutoLeo__c
     */
    @isTest
    static void testGetSinistriWithNullIdAutoLeo() {
        Asset assetWithNullId = [SELECT Id FROM Asset WHERE Name = 'Test Asset Null ID' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => assetWithNullId.Id
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.getSinistri(input, output, options);
        Test.stopTest();

        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assert(result.containsKey('status'), 'Result should contain status');

        String status = (String) result.get('status');
        System.assert(status == 'ERROR' || status == 'DATA_CLOUD_UNAVAILABLE',
                     'Status should be ERROR or DATA_CLOUD_UNAVAILABLE for null idAutoLeo');
    }

    /**
     * @description Test getSinistri with Asset that has invalid idAutoLeo__c
     */
    @isTest
    static void testGetSinistriWithInvalidIdAutoLeo() {
        Asset assetWithInvalidId = [SELECT Id FROM Asset WHERE Name = 'Test Asset Invalid ID' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => assetWithInvalidId.Id
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.getSinistri(input, output, options);
        Test.stopTest();

        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assert(result.containsKey('status'), 'Result should contain status');

        String status = (String) result.get('status');
        System.assert(status == 'ERROR' || status == 'DATA_CLOUD_UNAVAILABLE',
                     'Status should be ERROR or DATA_CLOUD_UNAVAILABLE for invalid idAutoLeo');
    }

    /**
     * @description Test Data Cloud unavailable scenario specifically
     */
    @isTest
    static void testDataCloudUnavailableScenario() {
        // This test will naturally handle the Data Cloud unavailable scenario
        // if the UR_SINISTRI__dlm object doesn't exist in the test org
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];

        Map<String, Object> input = new Map<String, Object>{
            'recordId' => testAsset.Id
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.getSinistri(input, output, options);
        Test.stopTest();

        // Assertions
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assert(result.containsKey('status'), 'Result should contain status');
        System.assert(result.containsKey('message'), 'Result should contain message');
        System.assert(result.containsKey('records'), 'Result should contain records');

        // If Data Cloud is unavailable, verify the response structure
        String status = (String) result.get('status');
        if (status == 'DATA_CLOUD_UNAVAILABLE') {
            System.assert(result.containsKey('isDataCloudAvailable'), 'Should contain isDataCloudAvailable flag');
            System.assert(result.containsKey('userFriendlyMessage'), 'Should contain userFriendlyMessage');
            System.assert(result.containsKey('recordCount'), 'Should contain recordCount');

            Boolean isDataCloudAvailable = (Boolean) result.get('isDataCloudAvailable');
            System.assertEquals(false, isDataCloudAvailable, 'isDataCloudAvailable should be false');

            Integer recordCount = (Integer) result.get('recordCount');
            System.assertEquals(0, recordCount, 'recordCount should be 0');

            List<sObject> records = (List<sObject>) result.get('records');
            System.assertEquals(0, records.size(), 'Records list should be empty');
        }
    }

    /**
     * @description Test exception handling in invokeMethod
     */
    @isTest
    static void testInvokeMethodExceptionHandling() {
        // Prepare test parameters that will cause an exception (null Set Values)
        Map<String, Object> input = new Map<String, Object>{
            'recordId' => 'invalid',
            'Set Values' => null
        };

        Map<String, Object> output = new Map<String, Object>();
        Map<String, Object> options = new Map<String, Object>();

        Test.startTest();
        Map<String, Object> result = urcs_DC_GetSinistri.invokeMethod(input, output, options);
        Test.stopTest();

        // Assertions - should handle exception and put error in output
        System.assertNotEquals(null, result, 'Result should not be null');
        System.assert(result.containsKey('error'), 'Result should contain error key');

        String errorMessage = (String) result.get('error');
        System.assertNotEquals(null, errorMessage, 'Error message should not be null');
        System.assert(errorMessage.length() > 0, 'Error message should not be empty');
    }

    /**
     * @description Test method case sensitivity
     */
    @isTest
    static void testMethodCaseSensitivity() {
        Asset testAsset = [SELECT Id FROM Asset WHERE Name = 'Test Vehicle Asset' LIMIT 1];

        // Test with different case variations
        List<String> methodVariations = new List<String>{'INIT', 'Init', 'iNiT'};

        for (String methodName : methodVariations) {
            Map<String, Object> input = new Map<String, Object>{
                'recordId' => testAsset.Id,
                'Set Values' => new Map<String, Object>{
                    'methodExecute' => methodName
                }
            };

            Map<String, Object> output = new Map<String, Object>();
            Map<String, Object> options = new Map<String, Object>();

            Test.startTest();
            Map<String, Object> result = urcs_DC_GetSinistri.invokeMethod(input, output, options);
            Test.stopTest();

            // All variations should work due to equalsIgnoreCase
            System.assert(result.containsKey('status'),
                'Method ' + methodName + ' should execute getSinistri and return status');
        }
    }
}
