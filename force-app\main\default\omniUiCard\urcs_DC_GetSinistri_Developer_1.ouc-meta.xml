<?xml version="1.0" encoding="UTF-8"?>
<OmniUiCard xmlns="http://soap.sforce.com/2006/04/metadata">
    <authorName>Developer</authorName>
    <clonedFromOmniUiCardKey>urcs_TabellaSinistri/Developer/3.0</clonedFromOmniUiCardKey>
    <dataSourceConfig>{&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;Remote\&quot;][\&quot;records\&quot;]&quot;,&quot;ipMethod&quot;:&quot;URCS_Sinistri&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;02i9V000006yF7BQAU&quot;,&quot;id&quot;:1}]}}</dataSourceConfig>
    <isActive>true</isActive>
    <isManagedUsingStdDesigner>false</isManagedUsingStdDesigner>
    <name>urcs_DC_GetSinistri</name>
    <omniUiCardType>Parent</omniUiCardType>
    <propertySetConfig>{&quot;states&quot;:[{&quot;fields&quot;:[],&quot;conditions&quot;:{&quot;id&quot;:&quot;state-condition-object&quot;,&quot;isParent&quot;:true,&quot;group&quot;:[]},&quot;definedActions&quot;:{&quot;actions&quot;:[]},&quot;name&quot;:&quot;Active&quot;,&quot;isSmartAction&quot;:false,&quot;smartAction&quot;:{},&quot;styleObject&quot;:{&quot;padding&quot;:[{&quot;type&quot;:&quot;around&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;margin&quot;:[{&quot;type&quot;:&quot;bottom&quot;,&quot;size&quot;:&quot;x-small&quot;}],&quot;container&quot;:{&quot;class&quot;:&quot;slds-card&quot;},&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;,&quot;class&quot;:&quot;slds-card slds-p-around_x-small slds-m-bottom_x-small&quot;},&quot;components&quot;:{&quot;layer-0&quot;:{&quot;children&quot;:[{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:&quot;false&quot;,&quot;issortavailable&quot;:&quot;true&quot;,&quot;records&quot;:&quot;{records}&quot;,&quot;columns&quot;:[]},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Data Table-0&quot;,&quot;uKey&quot;:&quot;1752138182729-330&quot;,&quot;datasourceKey&quot;:&quot;state0element0&quot;},{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:&quot;false&quot;,&quot;issortavailable&quot;:&quot;true&quot;,&quot;records&quot;:&quot;{records}&quot;,&quot;columns&quot;:[]},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Data Table-1&quot;,&quot;uKey&quot;:&quot;1752138182729-778&quot;,&quot;datasourceKey&quot;:&quot;state0element1&quot;},{&quot;name&quot;:&quot;Data Table&quot;,&quot;element&quot;:&quot;flexDatatable&quot;,&quot;size&quot;:{&quot;isResponsive&quot;:false,&quot;default&quot;:&quot;12&quot;},&quot;stateIndex&quot;:0,&quot;class&quot;:&quot;slds-col &quot;,&quot;property&quot;:{&quot;record&quot;:&quot;{record}&quot;,&quot;card&quot;:&quot;{card}&quot;,&quot;issearchavailable&quot;:false,&quot;issortavailable&quot;:true,&quot;cellLevelEdit&quot;:true,&quot;pagelimit&quot;:3,&quot;groupOrder&quot;:&quot;asc&quot;,&quot;searchDatatable&quot;:&quot;&quot;,&quot;columns&quot;:[{&quot;fieldName&quot;:&quot;ID_SINISTRO__c&quot;,&quot;label&quot;:&quot;Cod. Sinistro&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;ID_SINISTRO_WB__c&quot;,&quot;label&quot;:&quot;Cod. Sinistro WB&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;DS_STATO_SINISTRO__c&quot;,&quot;label&quot;:&quot;Descrizione stato sinistro&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;ID_CONTRATTO__c&quot;,&quot;label&quot;:&quot;id contratto&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;ID_AUTO__c&quot;,&quot;label&quot;:&quot;Id Auto&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;,&quot;label&quot;:&quot;Denominazione Driver&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;INTERVENTO_DATA_INVIO__c&quot;,&quot;label&quot;:&quot;Intervento Data Invio&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;DS_ULTIMA_SORGENTE__c&quot;,&quot;label&quot;:&quot;Ultima sorgente&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;text&quot;},{&quot;fieldName&quot;:&quot;TS_INVIO_WB__c&quot;,&quot;label&quot;:&quot;Data creazione in Leonardo&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;date&quot;,&quot;format&quot;:&quot;DD/MM/YYYY&quot;},{&quot;fieldName&quot;:&quot;TS_DENUNCIA__c&quot;,&quot;label&quot;:&quot;Denuncia ricevuta il&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;date&quot;,&quot;format&quot;:&quot;DD/MM/YYYY&quot;},{&quot;fieldName&quot;:&quot;TS_CREAZIONE__c&quot;,&quot;label&quot;:&quot;Creato il&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;datetime&quot;,&quot;format&quot;:&quot;DD/MM/YYYY, HH:mm&quot;},{&quot;fieldName&quot;:&quot;TS_CHIUSURA__c&quot;,&quot;label&quot;:&quot;Chiuso il&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;date&quot;,&quot;format&quot;:&quot;DD/MM/YYYY&quot;},{&quot;fieldName&quot;:&quot;DS_CANALE_APERTURA__c&quot;,&quot;label&quot;:&quot;Canale di Apertura&quot;,&quot;searchable&quot;:true,&quot;sortable&quot;:true,&quot;type&quot;:&quot;textarea&quot;,&quot;format&quot;:&quot;DD/MM/YYYY&quot;}],&quot;records&quot;:&quot;{records}&quot;},&quot;type&quot;:&quot;element&quot;,&quot;styleObject&quot;:{&quot;sizeClass&quot;:&quot;slds-size_12-of-12&quot;},&quot;elementLabel&quot;:&quot;Data Table-2&quot;,&quot;uKey&quot;:&quot;1752138182729-695&quot;,&quot;datasourceKey&quot;:&quot;state0element2&quot;}]}},&quot;childCards&quot;:[],&quot;actions&quot;:[],&quot;omniscripts&quot;:[],&quot;documents&quot;:[]}],&quot;dataSource&quot;:{&quot;type&quot;:&quot;IntegrationProcedures&quot;,&quot;value&quot;:{&quot;dsDelay&quot;:&quot;&quot;,&quot;resultVar&quot;:&quot;[\&quot;Remote\&quot;][\&quot;records\&quot;]&quot;,&quot;ipMethod&quot;:&quot;URCS_Sinistri&quot;,&quot;vlocityAsync&quot;:false,&quot;inputMap&quot;:{&quot;recordId&quot;:&quot;{recordId}&quot;},&quot;jsonMap&quot;:&quot;{\&quot;recordId\&quot;:\&quot;{recordId}\&quot;}&quot;},&quot;orderBy&quot;:{&quot;name&quot;:&quot;&quot;,&quot;isReverse&quot;:&quot;&quot;},&quot;contextVariables&quot;:[{&quot;name&quot;:&quot;recordId&quot;,&quot;val&quot;:&quot;02i9V000006yF7BQAU&quot;,&quot;id&quot;:1}]},&quot;title&quot;:&quot;urcs_DC_GetSinistri&quot;,&quot;enableLwc&quot;:true,&quot;isFlex&quot;:true,&quot;theme&quot;:&quot;slds&quot;,&quot;selectableMode&quot;:&quot;Multi&quot;,&quot;xmlObject&quot;:{&quot;masterLabel&quot;:&quot;urcs_TabellaSinistri&quot;,&quot;apiVersion&quot;:61,&quot;targetConfigs&quot;:&quot;CiAgICA8dGFyZ2V0Q29uZmlnIHhtbG5zPSJodHRwOi8vc29hcC5zZm9yY2UuY29tLzIwMDYvMDQvbWV0YWRhdGEiIHRhcmdldHM9ImxpZ2h0bmluZ19fQXBwUGFnZSI+CiAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICA8cHJvcGVydHkgbmFtZT0icmVjb3JkSWQiIHR5cGU9IlN0cmluZyIvPgogICAgPC90YXJnZXRDb25maWc+CiAgICA8dGFyZ2V0Q29uZmlnIHhtbG5zPSJodHRwOi8vc29hcC5zZm9yY2UuY29tLzIwMDYvMDQvbWV0YWRhdGEiIHRhcmdldHM9ImxpZ2h0bmluZ19fUmVjb3JkUGFnZSI+CiAgICAgIDxwcm9wZXJ0eSBuYW1lPSJkZWJ1ZyIgdHlwZT0iQm9vbGVhbiIvPgogICAgICA8b2JqZWN0cyB4bWxucz0iIj4KICAgICAgICA8b2JqZWN0PkFzc2V0PC9vYmplY3Q+CiAgICAgIDwvb2JqZWN0cz4KICAgIDwvdGFyZ2V0Q29uZmlnPgogIA==&quot;,&quot;targets&quot;:{&quot;target&quot;:[&quot;lightning__RecordPage&quot;,&quot;lightning__AppPage&quot;,&quot;lightning__HomePage&quot;]},&quot;isExplicitImport&quot;:false,&quot;description&quot;:&quot;&quot;},&quot;xmlJson&quot;:[{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__AppPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}},{&quot;@attributes&quot;:{&quot;name&quot;:&quot;recordId&quot;,&quot;type&quot;:&quot;String&quot;}}]},{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;http://soap.sforce.com/2006/04/metadata&quot;,&quot;targets&quot;:&quot;lightning__RecordPage&quot;},&quot;property&quot;:[{&quot;@attributes&quot;:{&quot;name&quot;:&quot;debug&quot;,&quot;type&quot;:&quot;Boolean&quot;}}],&quot;objects&quot;:{&quot;@attributes&quot;:{&quot;xmlns&quot;:&quot;&quot;},&quot;object&quot;:&quot;Asset&quot;}}]}</propertySetConfig>
    <sampleDataSourceResponse>{&quot;RemoteStatus&quot;:true,&quot;Set ValuesStatus&quot;:true,&quot;Remote&quot;:{&quot;error&quot;:&quot;OK&quot;,&quot;errorCode&quot;:&quot;INVOKE-200&quot;,&quot;records&quot;:[{&quot;DS_CANALE_APERTURA__c&quot;:&quot;Leonardo&quot;,&quot;TS_CHIUSURA__c&quot;:&quot;2016-03-09T15:29:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2016-03-09T15:22:00.000+0000&quot;,&quot;TS_DENUNCIA__c&quot;:&quot;2016-03-09T00:00:00.000+0000&quot;,&quot;TS_INVIO_WB__c&quot;:null,&quot;DS_ULTIMA_SORGENTE__c&quot;:&quot;LEO&quot;,&quot;INTERVENTO_DATA_INVIO__c&quot;:&quot;2436939 | Non inviato&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;Renosto Stefano&quot;,&quot;ID_AUTO__c&quot;:434076,&quot;ID_CONTRATTO__c&quot;:1073479,&quot;DS_STATO_SINISTRO__c&quot;:&quot;Certificato&quot;,&quot;ID_SINISTRO_WB__c&quot;:null,&quot;ID_SINISTRO__c&quot;:192488,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_SINISTRI__dlm/qDS000007666988GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}},{&quot;DS_CANALE_APERTURA__c&quot;:&quot;Leonardo&quot;,&quot;TS_CHIUSURA__c&quot;:&quot;2017-09-01T00:00:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2017-09-01T12:00:00.000+0000&quot;,&quot;TS_DENUNCIA__c&quot;:&quot;2017-09-01T00:00:00.000+0000&quot;,&quot;TS_INVIO_WB__c&quot;:null,&quot;DS_ULTIMA_SORGENTE__c&quot;:&quot;LEO&quot;,&quot;INTERVENTO_DATA_INVIO__c&quot;:&quot;3048932 | Non inviato&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;Goi Enedino&quot;,&quot;ID_AUTO__c&quot;:405287,&quot;ID_CONTRATTO__c&quot;:1095593,&quot;DS_STATO_SINISTRO__c&quot;:&quot;Certificato&quot;,&quot;ID_SINISTRO_WB__c&quot;:null,&quot;ID_SINISTRO__c&quot;:238690,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_SINISTRI__dlm/qDS000004066584GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}},{&quot;DS_CANALE_APERTURA__c&quot;:&quot;Leonardo&quot;,&quot;TS_CHIUSURA__c&quot;:&quot;2018-09-06T00:00:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2018-09-06T10:03:00.000+0000&quot;,&quot;TS_DENUNCIA__c&quot;:&quot;2018-09-06T00:00:00.000+0000&quot;,&quot;TS_INVIO_WB__c&quot;:null,&quot;DS_ULTIMA_SORGENTE__c&quot;:&quot;LEO&quot;,&quot;INTERVENTO_DATA_INVIO__c&quot;:&quot;3549491 | Non inviato&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;Servizi Generali&quot;,&quot;ID_AUTO__c&quot;:465565,&quot;ID_CONTRATTO__c&quot;:1098189,&quot;DS_STATO_SINISTRO__c&quot;:&quot;Certificato&quot;,&quot;ID_SINISTRO_WB__c&quot;:null,&quot;ID_SINISTRO__c&quot;:280715,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_SINISTRI__dlm/qDS000008274483GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}},{&quot;DS_CANALE_APERTURA__c&quot;:&quot;Leonardo&quot;,&quot;TS_CHIUSURA__c&quot;:&quot;2018-10-02T15:24:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2018-10-02T12:28:00.000+0000&quot;,&quot;TS_DENUNCIA__c&quot;:&quot;2018-10-02T00:00:00.000+0000&quot;,&quot;TS_INVIO_WB__c&quot;:null,&quot;DS_ULTIMA_SORGENTE__c&quot;:&quot;LEO&quot;,&quot;INTERVENTO_DATA_INVIO__c&quot;:&quot;3573576 | Non inviato&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;Ferri Roberto&quot;,&quot;ID_AUTO__c&quot;:463560,&quot;ID_CONTRATTO__c&quot;:1105111,&quot;DS_STATO_SINISTRO__c&quot;:&quot;Certificato&quot;,&quot;ID_SINISTRO_WB__c&quot;:null,&quot;ID_SINISTRO__c&quot;:283708,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_SINISTRI__dlm/qDS000004135028GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}},{&quot;DS_CANALE_APERTURA__c&quot;:&quot;Leonardo&quot;,&quot;TS_CHIUSURA__c&quot;:&quot;2018-11-09T15:32:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2018-10-03T11:56:00.000+0000&quot;,&quot;TS_DENUNCIA__c&quot;:&quot;2018-10-03T00:00:00.000+0000&quot;,&quot;TS_INVIO_WB__c&quot;:null,&quot;DS_ULTIMA_SORGENTE__c&quot;:&quot;LEO&quot;,&quot;INTERVENTO_DATA_INVIO__c&quot;:&quot;3574960 | Non inviato&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;Servizi Generali&quot;,&quot;ID_AUTO__c&quot;:437744,&quot;ID_CONTRATTO__c&quot;:1084181,&quot;DS_STATO_SINISTRO__c&quot;:&quot;Certificato&quot;,&quot;ID_SINISTRO_WB__c&quot;:null,&quot;ID_SINISTRO__c&quot;:283859,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_SINISTRI__dlm/qDS000005208465GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}},{&quot;DS_CANALE_APERTURA__c&quot;:&quot;Leonardo&quot;,&quot;TS_CHIUSURA__c&quot;:&quot;2019-05-23T15:47:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2019-05-23T15:46:00.000+0000&quot;,&quot;TS_DENUNCIA__c&quot;:&quot;2019-05-23T00:00:00.000+0000&quot;,&quot;TS_INVIO_WB__c&quot;:null,&quot;DS_ULTIMA_SORGENTE__c&quot;:&quot;LEO&quot;,&quot;INTERVENTO_DATA_INVIO__c&quot;:&quot;3971183 | Non inviato&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;Servizi Generali&quot;,&quot;ID_AUTO__c&quot;:494385,&quot;ID_CONTRATTO__c&quot;:1120500,&quot;DS_STATO_SINISTRO__c&quot;:&quot;Certificato&quot;,&quot;ID_SINISTRO_WB__c&quot;:null,&quot;ID_SINISTRO__c&quot;:313425,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_SINISTRI__dlm/qDS000006673028GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}},{&quot;DS_CANALE_APERTURA__c&quot;:&quot;Leonardo&quot;,&quot;TS_CHIUSURA__c&quot;:&quot;2018-06-19T17:00:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2018-06-19T17:00:00.000+0000&quot;,&quot;TS_DENUNCIA__c&quot;:&quot;2018-06-19T00:00:00.000+0000&quot;,&quot;TS_INVIO_WB__c&quot;:null,&quot;DS_ULTIMA_SORGENTE__c&quot;:&quot;LEO&quot;,&quot;INTERVENTO_DATA_INVIO__c&quot;:&quot;3468843 | Non inviato&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;Servizi Generali .&quot;,&quot;ID_AUTO__c&quot;:471962,&quot;ID_CONTRATTO__c&quot;:1102465,&quot;DS_STATO_SINISTRO__c&quot;:&quot;Certificato&quot;,&quot;ID_SINISTRO_WB__c&quot;:null,&quot;ID_SINISTRO__c&quot;:272107,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_SINISTRI__dlm/qDS000007539891GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}},{&quot;DS_CANALE_APERTURA__c&quot;:&quot;Leonardo&quot;,&quot;TS_CHIUSURA__c&quot;:&quot;2019-05-13T10:13:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2019-05-13T10:10:00.000+0000&quot;,&quot;TS_DENUNCIA__c&quot;:&quot;2019-05-13T00:00:00.000+0000&quot;,&quot;TS_INVIO_WB__c&quot;:null,&quot;DS_ULTIMA_SORGENTE__c&quot;:&quot;LEO&quot;,&quot;INTERVENTO_DATA_INVIO__c&quot;:&quot;3956044 | Non inviato&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;Furlani Andrea&quot;,&quot;ID_AUTO__c&quot;:480790,&quot;ID_CONTRATTO__c&quot;:1109530,&quot;DS_STATO_SINISTRO__c&quot;:&quot;Certificato&quot;,&quot;ID_SINISTRO_WB__c&quot;:null,&quot;ID_SINISTRO__c&quot;:311731,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_SINISTRI__dlm/qDS000002211236GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}},{&quot;DS_CANALE_APERTURA__c&quot;:&quot;Leonardo&quot;,&quot;TS_CHIUSURA__c&quot;:&quot;2018-08-03T15:00:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2018-08-03T15:00:00.000+0000&quot;,&quot;TS_DENUNCIA__c&quot;:&quot;2018-08-03T00:00:00.000+0000&quot;,&quot;TS_INVIO_WB__c&quot;:null,&quot;DS_ULTIMA_SORGENTE__c&quot;:&quot;LEO&quot;,&quot;INTERVENTO_DATA_INVIO__c&quot;:&quot;3527039 | Non inviato&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;Servizi Generali&quot;,&quot;ID_AUTO__c&quot;:424581,&quot;ID_CONTRATTO__c&quot;:1119472,&quot;DS_STATO_SINISTRO__c&quot;:&quot;Certificato&quot;,&quot;ID_SINISTRO_WB__c&quot;:null,&quot;ID_SINISTRO__c&quot;:277792,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_SINISTRI__dlm/qDS000001410304GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}},{&quot;DS_CANALE_APERTURA__c&quot;:&quot;Leonardo&quot;,&quot;TS_CHIUSURA__c&quot;:&quot;2018-09-25T08:40:00.000+0000&quot;,&quot;TS_CREAZIONE__c&quot;:&quot;2018-09-05T11:46:00.000+0000&quot;,&quot;TS_DENUNCIA__c&quot;:&quot;2018-09-05T00:00:00.000+0000&quot;,&quot;TS_INVIO_WB__c&quot;:null,&quot;DS_ULTIMA_SORGENTE__c&quot;:&quot;LEO&quot;,&quot;INTERVENTO_DATA_INVIO__c&quot;:&quot;3548743 | Non inviato&quot;,&quot;DS_DRIVER_DENOMINAZIONECOMPUTED__c&quot;:&quot;C.P. Il Paiolo (Mn)&quot;,&quot;ID_AUTO__c&quot;:459797,&quot;ID_CONTRATTO__c&quot;:1093246,&quot;DS_STATO_SINISTRO__c&quot;:&quot;Certificato&quot;,&quot;ID_SINISTRO_WB__c&quot;:null,&quot;ID_SINISTRO__c&quot;:280588,&quot;attributes&quot;:{&quot;url&quot;:&quot;/services/data/v64.0/sobjects/UR_SINISTRI__dlm/qDS000006638861GAA&quot;,&quot;type&quot;:&quot;UR_SINISTRI__dlm&quot;}}]},&quot;Set Values&quot;:{&quot;methodExecute&quot;:&quot;init&quot;,&quot;recordId&quot;:&quot;02i9V000006yF7BQAU&quot;},&quot;options&quot;:{&quot;forceQueueable&quot;:false,&quot;mockHttpResponse&quot;:null,&quot;vlcApexResponse&quot;:true,&quot;useFuture&quot;:false,&quot;isTestProcedure&quot;:false,&quot;resetCache&quot;:false,&quot;integrationProcedureKey&quot;:null,&quot;vlcIPData&quot;:null,&quot;OmniAnalyticsTrackingDebug&quot;:false,&quot;ignoreCache&quot;:false,&quot;shouldCommit&quot;:false,&quot;vlcTestSuiteUniqueKey&quot;:null,&quot;vlcTestUniqueKey&quot;:null,&quot;vlcCacheKey&quot;:null,&quot;continuationStepResult&quot;:null,&quot;vlcFilesMap&quot;:null,&quot;ParentInteractionToken&quot;:null,&quot;useQueueable&quot;:false,&quot;disableMetadataCache&quot;:false,&quot;isDebug&quot;:false,&quot;queueableChainable&quot;:false,&quot;useContinuation&quot;:false,&quot;chainable&quot;:false,&quot;ignoreMetadataPermissions&quot;:false,&quot;useHttpCalloutMock&quot;:false,&quot;useQueueableApexRemoting&quot;:false},&quot;response&quot;:{},&quot;ResponseStatus&quot;:true,&quot;recordId&quot;:&quot;02i9V000006yF7BQAU&quot;}</sampleDataSourceResponse>
    <stylingConfiguration>{}</stylingConfiguration>
    <versionNumber>1</versionNumber>
</OmniUiCard>
