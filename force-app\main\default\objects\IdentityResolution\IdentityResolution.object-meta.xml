<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>ReadWrite</externalSharingModel>
    <searchLayouts>
        <customTabListAdditionalFields>Suffix</customTabListAdditionalFields>
        <customTabListAdditionalFields>DataSpace</customTabListAdditionalFields>
        <customTabListAdditionalFields>RootEntityLabel</customTabListAdditionalFields>
        <customTabListAdditionalFields>Status</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastRunStatus</customTabListAdditionalFields>
        <customTabListAdditionalFields>SourceCount</customTabListAdditionalFields>
        <customTabListAdditionalFields>MatchedCount</customTabListAdditionalFields>
        <customTabListAdditionalFields>UnifiedCount</customTabListAdditionalFields>
        <customTabListAdditionalFields>ConsolidationRate</customTabListAdditionalFields>
        <lookupDialogsAdditionalFields>Suffix</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>DataSpace</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>RootEntityLabel</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Status</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>LastRunStatus</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>SourceCount</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>MatchedCount</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>UnifiedCount</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>ConsolidationRate</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Suffix</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>DataSpace</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>RootEntityLabel</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Status</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LastRunStatus</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>SourceCount</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>MatchedCount</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>UnifiedCount</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ConsolidationRate</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>Suffix</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>DataSpace</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>RootEntityLabel</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Status</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastRunStatus</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>SourceCount</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>MatchedCount</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>UnifiedCount</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ConsolidationRate</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>ReadWrite</sharingModel>
</CustomObject>
