<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>Private</externalSharingModel>
    <searchLayouts>
        <lookupDialogsAdditionalFields>StartDateTime</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>DurationInSeconds</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>RelatedRecord</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>StartDateTime</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>DurationInSeconds</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>RelatedRecord</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>StartDateTime</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>DurationInSeconds</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Owner</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>RelatedRecord</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
