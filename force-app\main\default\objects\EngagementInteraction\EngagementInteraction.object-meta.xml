<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <searchLayouts>
        <customTabListAdditionalFields>InitiatingAttendee</customTabListAdditionalFields>
        <customTabListAdditionalFields>IsAttendeeAuthenticated</customTabListAdditionalFields>
        <customTabListAdditionalFields>IsAttendeeVerified</customTabListAdditionalFields>
        <customTabListAdditionalFields>StartDateTime</customTabListAdditionalFields>
        <customTabListAdditionalFields>EndDateTime</customTabListAdditionalFields>
        <customTabListAdditionalFields>CommunicationChannel</customTabListAdditionalFields>
        <customTabListAdditionalFields>Sentiment</customTabListAdditionalFields>
        <customTabListAdditionalFields>Context</customTabListAdditionalFields>
        <customTabListAdditionalFields>ExternalIdentifier</customTabListAdditionalFields>
        <customTabListAdditionalFields>Owner</customTabListAdditionalFields>
        <lookupDialogsAdditionalFields>InitiatingAttendee</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>IsAttendeeAuthenticated</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>IsAttendeeVerified</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>StartDateTime</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>EndDateTime</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>CommunicationChannel</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Sentiment</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Context</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>ExternalIdentifier</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Owner</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>InitiatingAttendee</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>IsAttendeeAuthenticated</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>IsAttendeeVerified</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>StartDateTime</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>EndDateTime</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CommunicationChannel</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Sentiment</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Context</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ExternalIdentifier</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Owner</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>PhoneNumber</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>InitiatingAttendee</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>IsAttendeeAuthenticated</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>IsAttendeeVerified</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>StartDateTime</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>EndDateTime</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CommunicationChannel</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Sentiment</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Context</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ExternalIdentifier</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Owner</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>ReadWrite</sharingModel>
</CustomObject>
