<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <searchLayouts>
        <customTabListAdditionalFields>Status</customTabListAdditionalFields>
        <customTabListAdditionalFields>HasAttachment</customTabListAdditionalFields>
        <customTabListAdditionalFields>Address</customTabListAdditionalFields>
        <customTabListAdditionalFields>Subject</customTabListAdditionalFields>
        <customTabListAdditionalFields>MessageDate</customTabListAdditionalFields>
        <lookupDialogsAdditionalFields>Status</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>HasAttachment</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Address</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Subject</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>MessageDate</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Status</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>HasAttachment</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Address</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Subject</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>MessageDate</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>FromName</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ToAddress</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Subject</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>MessageDate</searchResultsAdditionalFields>
    </searchLayouts>
</CustomObject>
