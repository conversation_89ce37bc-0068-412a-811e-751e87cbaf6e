<?xml version="1.0" encoding="UTF-8"?>
<FieldSet xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>PersonalInfo_EPIM</fullName>
    <description>Fieldset for User Field Masking</description>
    <displayedFields>
        <field>Title</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>FirstName</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>EmployeeNumber</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Name</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>GeocodeAccuracy</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Division</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Signature</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>LastName</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>FederationIdentifier</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>AboutMe</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Alias</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Username</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>MobilePhone</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>ManagerId</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>CompanyName</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Department</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>State</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Phone</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>BadgeText</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Longitude</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>SenderEmail</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Latitude</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>City</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Fax</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>PostalCode</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Street</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>SenderName</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Email</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Extension</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <displayedFields>
        <field>Country</field>
        <isFieldManaged>true</isFieldManaged>
        <isRequired>false</isRequired>
    </displayedFields>
    <label>PersonalInfo_EPIM</label>
</FieldSet>
