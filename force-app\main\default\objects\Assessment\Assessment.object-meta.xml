<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>Private</externalSharingModel>
    <searchLayouts>
        <customTabListAdditionalFields>LastModifiedDate</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastModifiedBy</customTabListAdditionalFields>
        <customTabListAdditionalFields>Account</customTabListAdditionalFields>
        <customTabListAdditionalFields>Case</customTabListAdditionalFields>
        <customTabListAdditionalFields>Parent</customTabListAdditionalFields>
        <customTabListAdditionalFields>Contact</customTabListAdditionalFields>
        <customTabListAdditionalFields>OmniProcess</customTabListAdditionalFields>
        <customTabListAdditionalFields>PartyProfile</customTabListAdditionalFields>
        <customTabListAdditionalFields>Identifier</customTabListAdditionalFields>
        <customTabListAdditionalFields>Assessor</customTabListAdditionalFields>
        <lookupDialogsAdditionalFields>LastModifiedDate</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>LastModifiedBy</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Account</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Case</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Parent</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Contact</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>OmniProcess</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>PartyProfile</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Identifier</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Assessor</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LastModifiedDate</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LastModifiedBy</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Account</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Case</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Parent</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Contact</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>OmniProcess</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>PartyProfile</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Identifier</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Assessor</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedDate</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedBy</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Account</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Case</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Parent</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Contact</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>OmniProcess</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>PartyProfile</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Identifier</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Assessor</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Private</sharingModel>
</CustomObject>
