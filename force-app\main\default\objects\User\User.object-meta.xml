<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CallHighlightAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CallHighlightAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CallHighlightAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>DeleteBannerPhotoAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>DeleteBannerPhotoAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>DeleteBannerPhotoAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EditUserAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EditUserAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EditUserAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EmailHighlightAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EmailHighlightAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>EmailHighlightAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>FreezeUserAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>FreezeUserAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>FreezeUserAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>LinkToSetupUserDetailAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>LinkToSetupUserDetailAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>LinkToSetupUserDetailAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ManagePermSetsAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ManagePermSetsAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ManagePermSetsAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>PasswordUnlockAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>PasswordUnlockAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>PasswordUnlockAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ResetPasswordAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ResetPasswordAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>ResetPasswordAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SmsHighlightAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SmsHighlightAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SmsHighlightAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>UpdateBannerPhotoAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>UpdateBannerPhotoAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>UpdateBannerPhotoAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>UserActivationAction</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>UserActivationAction</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>UserActivationAction</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <comment>Action override created by Lightning App Builder during activation.</comment>
        <content>FinServ__UserProfilePage</content>
        <formFactor>Large</formFactor>
        <skipRecordTypeSelect>false</skipRecordTypeSelect>
        <type>Flexipage</type>
    </actionOverrides>
    <compactLayoutAssignment>User_Layout</compactLayoutAssignment>
    <enableEnhancedLookup>true</enableEnhancedLookup>
    <enableFeeds>true</enableFeeds>
    <externalSharingModel>Private</externalSharingModel>
    <searchLayouts>
        <lookupDialogsAdditionalFields>FULL_NAME</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>CORE.USER_ROLE.NAME</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>FULL_NAME</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CORE.USER_ROLE.NAME</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CORE.USERS.PHONE</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CORE.USERS.EXTENSION</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CORE.USERS.FAX</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>CORE.USERS.CELL</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>FULL_NAME</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CORE.USERS.TITLE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CORE.USERS.PHONE</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>CORE.USERS.EMAIL</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>Read</sharingModel>
</CustomObject>
