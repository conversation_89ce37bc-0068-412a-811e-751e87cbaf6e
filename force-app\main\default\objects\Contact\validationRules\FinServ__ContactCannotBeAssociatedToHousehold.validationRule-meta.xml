<?xml version="1.0" encoding="UTF-8"?>
<ValidationRule xmlns="http://soap.sforce.com/2006/04/metadata">
    <fullName>FinServ__ContactCannotBeAssociatedToHousehold</fullName>
    <active>true</active>
    <description>Check to see if contact is associated directly to household</description>
    <errorConditionFormula>Account.RecordType.DeveloperName=&apos;IndustriesHousehold&apos;</errorConditionFormula>
    <errorMessage>Can&apos;t associate a lead or contact to a household account.</errorMessage>
</ValidationRule>
