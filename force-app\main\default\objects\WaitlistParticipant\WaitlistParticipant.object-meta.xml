<?xml version="1.0" encoding="UTF-8"?>
<CustomObject xmlns="http://soap.sforce.com/2006/04/metadata">
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>CancelEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Clone</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Delete</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Edit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>List</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>New</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>SaveEdit</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>Tab</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Large</formFactor>
        <type>Default</type>
    </actionOverrides>
    <actionOverrides>
        <actionName>View</actionName>
        <formFactor>Small</formFactor>
        <type>Default</type>
    </actionOverrides>
    <compactLayoutAssignment>SYSTEM</compactLayoutAssignment>
    <enableFeeds>false</enableFeeds>
    <externalSharingModel>ControlledByParent</externalSharingModel>
    <searchLayouts>
        <customTabListAdditionalFields>Waitlist</customTabListAdditionalFields>
        <customTabListAdditionalFields>Participant</customTabListAdditionalFields>
        <customTabListAdditionalFields>WorkType</customTabListAdditionalFields>
        <customTabListAdditionalFields>Description</customTabListAdditionalFields>
        <customTabListAdditionalFields>ServiceAppointment</customTabListAdditionalFields>
        <customTabListAdditionalFields>ServiceResource</customTabListAdditionalFields>
        <customTabListAdditionalFields>Status</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastModifiedBy</customTabListAdditionalFields>
        <customTabListAdditionalFields>LastModifiedDate</customTabListAdditionalFields>
        <lookupDialogsAdditionalFields>Waitlist</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Participant</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>WorkType</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Description</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>ServiceAppointment</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>ServiceResource</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>Status</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>LastModifiedBy</lookupDialogsAdditionalFields>
        <lookupDialogsAdditionalFields>LastModifiedDate</lookupDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Waitlist</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Participant</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>WorkType</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Description</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ServiceAppointment</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>ServiceResource</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>Status</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LastModifiedBy</lookupPhoneDialogsAdditionalFields>
        <lookupPhoneDialogsAdditionalFields>LastModifiedDate</lookupPhoneDialogsAdditionalFields>
        <searchResultsAdditionalFields>Waitlist</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Participant</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>WorkType</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Description</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ServiceAppointment</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>ServiceResource</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>Status</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedBy</searchResultsAdditionalFields>
        <searchResultsAdditionalFields>LastModifiedDate</searchResultsAdditionalFields>
    </searchLayouts>
    <sharingModel>ControlledByParent</sharingModel>
</CustomObject>
