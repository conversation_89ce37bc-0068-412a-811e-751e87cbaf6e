global class urcs_GetSinistriFlexCard implements System.Callable {
    
    public Object call(String action, Map<String,Object> args){
        Map<String,Object> input = (Map<String,Object>) args.get('input');
        Map<String,Object> output = (Map<String,Object>) args.get('output');
        Map<String,Object> options = (Map<String,Object>) args.get('options');
        invokeMethod(input, output, options);
        return null;
    }
    
    @AuraEnabled
    global static Map<String,Object> invokeMethod(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        
        system.debug('input '+ JSON.serialize(input));
        system.debug('output '+ JSON.serialize(output));
        system.debug('options '+ JSON.serialize(options));
        Map<String,Object> setValueMap = (Map<String,Object>) input.get('Set Values');
        String method = (String) setValueMap.get('methodExecute');
        try{
            if(method.equalsIgnoreCase('init')){
                output = getSinistri(input,output,options);
            }
        }catch(Exception e){
            output.put('error',e.getMessage() + ' ' + e.getStackTraceString());
        }
        return output;
    }
    
    public static Map<String,Object> getSinistri(Map<String,Object> input,Map<String,Object> output, Map<String,Object> options){
        String idVeicolo =  null != (String)input.get('recordId') ? (String)input.get('recordId') : null;
        List<Asset> ass = [SELECT id,idAutoLeo__c FROM Asset WHERE id=: idVeicolo];
        Integer numeroAuto = Integer.valueOf(ass[0].idAutoLeo__c);
        system.debug('idVeicolo '+ idVeicolo);
        //List<UR_SINISTRI__dlm> listSinistri = [SELECT Id,ID_AUTO__c,ID_SINISTRO__c, ID_CONTRATTO__c FROM UR_SINISTRI__dlm WHERE ID_AUTO__c =:numeroAuto LIMIT 2];
        List<UR_SINISTRI__dlm> listSinistri = [SELECT Id,ID_AUTO__c,ID_SINISTRO__c, ID_CONTRATTO__c FROM UR_SINISTRI__dlm  LIMIT 10];
        output.put('records',listSinistri);
        return output;
    }
    
    
    
}